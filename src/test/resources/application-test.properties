# 测试环境配置
server.port=6061
server.servlet.context-path=/api

# 数据库配置 - 使用内存数据库进行测试
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# Redis配置 - 使用嵌入式Redis进行测试
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=1

# 日志配置
logging.level.com.easymeeting=DEBUG
logging.level.org.springframework.data.redis=INFO
