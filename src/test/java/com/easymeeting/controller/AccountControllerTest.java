package com.easymeeting.controller;

import com.easymeeting.EasymeetingApplication;
import com.easymeeting.entity.vo.ApiResult;
import com.easymeeting.entity.vo.CaptchaVO;
import com.easymeeting.service.ICaptchaService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AccountController测试类
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@SpringBootTest(classes = EasymeetingApplication.class)
@ActiveProfiles("test")
public class AccountControllerTest {

    @Autowired
    private AccountController accountController;

    @Autowired
    private ICaptchaService captchaService;

    @Test
    public void testGetCaptcha() {
        // 测试获取验证码
        ApiResult<CaptchaVO> result = accountController.getCaptcha();
        
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertNotNull(result.getData());
        assertNotNull(result.getData().getCaptchaKey());
        assertNotNull(result.getData().getCaptchaImage());
        
        System.out.println("验证码Key: " + result.getData().getCaptchaKey());
        System.out.println("验证码图片长度: " + result.getData().getCaptchaImage().length());
    }

    @Test
    public void testCaptchaService() {
        // 测试验证码服务
        CaptchaVO captcha = captchaService.generateCaptcha();
        
        assertNotNull(captcha);
        assertNotNull(captcha.getCaptchaKey());
        assertNotNull(captcha.getCaptchaImage());
        
        System.out.println("生成的验证码Key: " + captcha.getCaptchaKey());
        System.out.println("验证码图片Base64前缀: " + captcha.getCaptchaImage().substring(0, 50) + "...");
    }
}
