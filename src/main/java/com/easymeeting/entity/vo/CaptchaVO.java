package com.easymeeting.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 验证码响应对象
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Data
@ApiModel(value = "CaptchaVO", description = "验证码响应对象")
public class CaptchaVO {

    @ApiModelProperty(value = "验证码唯一标识")
    private String captchaKey;

    @ApiModelProperty(value = "验证码图片Base64编码")
    private String captchaImage;

    public CaptchaVO() {
    }

    public CaptchaVO(String captchaKey, String captchaImage) {
        this.captchaKey = captchaKey;
        this.captchaImage = captchaImage;
    }
}
