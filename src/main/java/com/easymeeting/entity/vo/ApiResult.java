package com.easymeeting.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统一API响应结果
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Data
@ApiModel(value = "ApiResult", description = "统一API响应结果")
public class ApiResult<T> {

    @ApiModelProperty(value = "响应码")
    private Integer code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "响应数据")
    private T data;

    @ApiModelProperty(value = "时间戳")
    private Long timestamp;

    public ApiResult() {
        this.timestamp = System.currentTimeMillis();
    }

    public ApiResult(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    public ApiResult(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static <T> ApiResult<T> success() {
        return new ApiResult<>(200, "操作成功");
    }

    /**
     * 成功响应带数据
     */
    public static <T> ApiResult<T> success(T data) {
        return new ApiResult<>(200, "操作成功", data);
    }

    /**
     * 成功响应带消息和数据
     */
    public static <T> ApiResult<T> success(String message, T data) {
        return new ApiResult<>(200, message, data);
    }

    /**
     * 失败响应
     */
    public static <T> ApiResult<T> error() {
        return new ApiResult<>(500, "操作失败");
    }

    /**
     * 失败响应带消息
     */
    public static <T> ApiResult<T> error(String message) {
        return new ApiResult<>(500, message);
    }

    /**
     * 失败响应带码和消息
     */
    public static <T> ApiResult<T> error(Integer code, String message) {
        return new ApiResult<>(code, message);
    }

    /**
     * 参数错误
     */
    public static <T> ApiResult<T> badRequest(String message) {
        return new ApiResult<>(400, message);
    }

    /**
     * 未授权
     */
    public static <T> ApiResult<T> unauthorized(String message) {
        return new ApiResult<>(401, message);
    }

    /**
     * 禁止访问
     */
    public static <T> ApiResult<T> forbidden(String message) {
        return new ApiResult<>(403, message);
    }
}
