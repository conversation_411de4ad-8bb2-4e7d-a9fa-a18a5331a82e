package com.easymeeting.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 通用响应结果类
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Data
@ApiModel(value = "Result", description = "通用响应结果")
public class Result<T> {

    @ApiModelProperty(value = "状态码")
    private Integer code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "响应数据")
    private T data;

    @ApiModelProperty(value = "时间戳")
    private Long timestamp;

    @ApiModelProperty(value = "是否成功")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean success;

    @ApiModelProperty(value = "是否失败")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean error;

    /**
     * 私有构造函数
     */
    private Result() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 私有构造函数 - 成功响应
     */
    private Result(Integer code, String message, T data, boolean isSuccess) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;

    }

    /**
     * 成功响应 - 无参数
     * 默认状态码200，默认消息"操作成功"
     */
    public static <T> Result<T> success() {
        return new Result<>(200, "操作成功", null, true);
    }

    /**
     * 成功响应 - 仅数据
     * 默认状态码200，默认消息"操作成功"
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(200, "操作成功", data, true);
    }

    /**
     * 成功响应 - 自定义状态码
     * 默认消息"操作成功"
     */
    public static <T> Result<T> success(Integer code) {
        return new Result<>(code, "操作成功", null, true);
    }

    /**
     * 成功响应 - 自定义状态码和数据
     * 默认消息"操作成功"
     */
    public static <T> Result<T> success(Integer code, T data) {
        return new Result<>(code, "操作成功", data, true);
    }

    /**
     * 成功响应 - 自定义状态码、消息和数据
     */
    public static <T> Result<T> success(Integer code, String message, T data) {
        return new Result<>(code, message, data, true);
    }

    /**
     * 失败响应 - 无参数
     * 默认状态码500，默认消息"操作失败"
     */
    public static <T> Result<T> error() {
        return new Result<>(500, "操作失败", null, false);
    }

    /**
     * 失败响应 - 仅消息
     * 默认状态码500
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(500, message, null, false);
    }

    /**
     * 失败响应 - 自定义状态码
     * 默认消息"操作失败"
     */
    public static <T> Result<T> error(Integer code) {
        return new Result<>(code, "操作失败", null, false);
    }

    /**
     * 失败响应 - 自定义状态码和消息
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message, null, false);
    }

    /**
     * 失败响应 - 自定义状态码、消息和数据
     */
    public static <T> Result<T> error(Integer code, String message, T data) {
        return new Result<>(code, message, data, false);
    }

    /**
     * 判断是否成功
     * 根据success字段判断
     */
    public boolean isSuccess() {
        return this.success != null && this.success;
    }

    /**
     * 判断是否失败
     * 根据error字段判断
     */
    public boolean isError() {
        return this.error != null && this.error;
    }
}
