package com.easymeeting.controller;

import com.easymeeting.entity.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器 - 演示Result类的使用
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@RestController
@RequestMapping("/test")
@Tag(name = "测试接口", description = "用于测试Result类功能的接口")
public class TestController {

    @GetMapping("/success")
    @Operation(summary = "测试成功响应", description = "返回成功的响应结果")
    public Result<String> testSuccess() {
        return Result.success("这是成功的数据");
    }

    @GetMapping("/success-no-data")
    @Operation(summary = "测试成功响应-无数据", description = "返回成功但无数据的响应结果")
    public Result<Void> testSuccessNoData() {
        return Result.success();
    }

    @GetMapping("/success-custom-code")
    @Operation(summary = "测试成功响应-自定义状态码", description = "返回自定义状态码的成功响应")
    public Result<String> testSuccessCustomCode() {
        return Result.success(201, "创建成功", "新创建的资源");
    }

    @GetMapping("/error")
    @Operation(summary = "测试失败响应", description = "返回失败的响应结果")
    public Result<Void> testError() {
        return Result.error("这是一个错误消息");
    }

    @GetMapping("/error-custom-code")
    @Operation(summary = "测试失败响应-自定义状态码", description = "返回自定义状态码的失败响应")
    public Result<Void> testErrorCustomCode() {
        return Result.error(404, "资源未找到");
    }

    @GetMapping("/complex-data")
    @Operation(summary = "测试复杂数据响应", description = "返回包含复杂数据结构的响应")
    public Result<Map<String, Object>> testComplexData() {
        Map<String, Object> data = new HashMap<>();
        data.put("userId", "12345");
        data.put("username", "testuser");
        data.put("email", "<EMAIL>");
        data.put("roles", new String[]{"user", "admin"});

        return Result.success(data);
    }

    @GetMapping("/conditional")
    @Operation(summary = "测试条件响应", description = "根据参数条件返回成功或失败响应")
    public Result<String> testConditional(
            @Parameter(description = "是否成功", example = "true")
            @RequestParam(defaultValue = "true") boolean success) {
        if (success) {
            return Result.success("条件满足，操作成功");
        } else {
            return Result.error(400, "条件不满足，操作失败");
        }
    }
}
