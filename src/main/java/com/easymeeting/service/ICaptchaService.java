package com.easymeeting.service;

import com.easymeeting.entity.vo.CaptchaVO;

/**
 * 验证码服务接口
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
public interface ICaptchaService {

    /**
     * 生成验证码
     *
     * @return 验证码信息
     */
    CaptchaVO generateCaptcha();

    /**
     * 验证验证码
     *
     * @param captchaKey 验证码唯一标识
     * @param captchaCode 用户输入的验证码
     * @return 验证结果
     */
    boolean verifyCaptcha(String captchaKey, String captchaCode);

    /**
     * 删除验证码
     *
     * @param captchaKey 验证码唯一标识
     */
    void removeCaptcha(String captchaKey);
}
