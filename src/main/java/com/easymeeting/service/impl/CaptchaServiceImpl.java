package com.easymeeting.service.impl;

import com.easymeeting.entity.vo.CaptchaVO;
import com.easymeeting.service.ICaptchaService;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Slf4j
@Service
public class CaptchaServiceImpl implements ICaptchaService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 验证码Redis前缀
     */
    private static final String CAPTCHA_PREFIX = "captcha:";

    /**
     * 验证码过期时间（分钟）
     */
    private static final int CAPTCHA_EXPIRE_MINUTES = 5;

    /**
     * 验证码长度
     */
    private static final int CAPTCHA_LENGTH = 4;

    /**
     * 验证码图片宽度
     */
    private static final int CAPTCHA_WIDTH = 130;

    /**
     * 验证码图片高度
     */
    private static final int CAPTCHA_HEIGHT = 48;

    @Override
    public CaptchaVO generateCaptcha() {
        try {
            // 生成验证码唯一标识
            String captchaKey = UUID.randomUUID().toString().replace("-", "");
            
            // 创建验证码对象
            SpecCaptcha captcha = new SpecCaptcha(CAPTCHA_WIDTH, CAPTCHA_HEIGHT, CAPTCHA_LENGTH);
            captcha.setCharType(Captcha.TYPE_DEFAULT);
            
            // 获取验证码文本
            String captchaText = captcha.text().toLowerCase();
            
            // 获取验证码图片Base64编码
            String captchaImage = captcha.toBase64();
            
            // 将验证码存储到Redis，设置过期时间
            String redisKey = CAPTCHA_PREFIX + captchaKey;
            stringRedisTemplate.opsForValue().set(redisKey, captchaText, CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            log.info("生成验证码成功，captchaKey: {}, captchaText: {}", captchaKey, captchaText);
            
            return new CaptchaVO(captchaKey, captchaImage);
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            throw new RuntimeException("生成验证码失败");
        }
    }

    @Override
    public boolean verifyCaptcha(String captchaKey, String captchaCode) {
        if (StringUtils.isBlank(captchaKey) || StringUtils.isBlank(captchaCode)) {
            log.warn("验证码参数为空，captchaKey: {}, captchaCode: {}", captchaKey, captchaCode);
            return false;
        }
        
        try {
            String redisKey = CAPTCHA_PREFIX + captchaKey;
            String storedCaptcha = stringRedisTemplate.opsForValue().get(redisKey);
            
            if (StringUtils.isBlank(storedCaptcha)) {
                log.warn("验证码已过期或不存在，captchaKey: {}", captchaKey);
                return false;
            }
            
            // 验证码不区分大小写
            boolean isValid = storedCaptcha.equalsIgnoreCase(captchaCode.trim());
            
            if (isValid) {
                log.info("验证码验证成功，captchaKey: {}", captchaKey);
                // 验证成功后删除验证码
                stringRedisTemplate.delete(redisKey);
            } else {
                log.warn("验证码验证失败，captchaKey: {}, expected: {}, actual: {}", 
                        captchaKey, storedCaptcha, captchaCode);
            }
            
            return isValid;
        } catch (Exception e) {
            log.error("验证验证码时发生异常，captchaKey: {}", captchaKey, e);
            return false;
        }
    }

    @Override
    public void removeCaptcha(String captchaKey) {
        if (StringUtils.isBlank(captchaKey)) {
            return;
        }
        
        try {
            String redisKey = CAPTCHA_PREFIX + captchaKey;
            stringRedisTemplate.delete(redisKey);
            log.info("删除验证码成功，captchaKey: {}", captchaKey);
        } catch (Exception e) {
            log.error("删除验证码失败，captchaKey: {}", captchaKey, e);
        }
    }
}
